# Frontend Production Environment Variables
# Copy this file to .env.production in the frontend directory

# =============================================================================
# API CONFIGURATION
# =============================================================================
# Base API URL (relative path for same-domain deployment)
VITE_API_BASE_URL=/api

# Backend URL (full domain for production)
VITE_BACKEND_URL=https://foobar.com

# WebSocket URL for real-time features
VITE_WS_URL=wss://foobar.com

# =============================================================================
# APPLICATION CONFIGURATION
# =============================================================================
# Application title
VITE_APP_TITLE=WBS Aistech

# Application version
VITE_APP_VERSION=1.0.0

# Environment name
VITE_APP_ENV=production

# =============================================================================
# FEATURE FLAGS
# =============================================================================
# Enable/disable features in production
VITE_FEATURE_CHAT=true
VITE_FEATURE_FILE_UPLOAD=true
VITE_FEATURE_NOTIFICATIONS=true
VITE_FEATURE_DARK_MODE=true
VITE_FEATURE_ANALYTICS=true

# =============================================================================
# EXTERNAL SERVICES
# =============================================================================
# Google Analytics (if used)
VITE_GA_TRACKING_ID=G-XXXXXXXXXX

# Sentry for error tracking (if used)
VITE_SENTRY_DSN=https://<EMAIL>/project-id

# =============================================================================
# UI CONFIGURATION
# =============================================================================
# Default language
VITE_DEFAULT_LOCALE=id

# Available languages
VITE_AVAILABLE_LOCALES=id,en

# Theme configuration
VITE_DEFAULT_THEME=light

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================
# Content Security Policy (if needed)
VITE_CSP_NONCE=

# =============================================================================
# PERFORMANCE CONFIGURATION
# =============================================================================
# API timeout in milliseconds
VITE_API_TIMEOUT=30000

# Request retry attempts
VITE_API_RETRY_ATTEMPTS=3

# Cache duration for static assets (in seconds)
VITE_CACHE_DURATION=86400

# =============================================================================
# DEBUGGING (Disabled for production)
# =============================================================================
# Debug mode
VITE_DEBUG=false

# Show API logs
VITE_LOG_API_CALLS=false

# Show performance metrics
VITE_SHOW_PERFORMANCE=false
