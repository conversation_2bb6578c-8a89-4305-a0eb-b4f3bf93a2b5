# Production Environment Variables for WBS Aistech
# Copy this file to .env.prod and update the values

# =============================================================================
# APPLICATION CONFIGURATION
# =============================================================================
NODE_ENV=production
APP_PORT=3000

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
# PostgreSQL Database Settings
DB_HOST=postgres
DB_PORT=5432
DB_NAME=csrit_backend
DB_USER=postgres
DB_PASS=CHANGE_THIS_SECURE_PASSWORD

# Database Connection Pool Settings
DB_POOL_MIN=2
DB_POOL_MAX=10
DB_POOL_IDLE_TIMEOUT=30000
DB_POOL_ACQUIRE_TIMEOUT=60000

# =============================================================================
# JWT AUTHENTICATION
# =============================================================================
# Generate secure random strings for production
# Use: openssl rand -base64 32
JWT_ACCESS_TOKEN_SECRET=CHANGE_THIS_TO_SECURE_RANDOM_STRING
JWT_REFRESH_TOKEN_SECRET=CHANGE_THIS_TO_DIFFERENT_SECURE_RANDOM_STRING
JWT_ACCESS_TOKEN_EXPIRATION_TIME=3600
JWT_REFRESH_TOKEN_EXPIRATION_TIME=86400

# =============================================================================
# CORS CONFIGURATION
# =============================================================================
# Production domain
CORS_ORIGIN=https://foobar.com



# =============================================================================
# EMAIL CONFIGURATION (Optional)
# =============================================================================
# SMTP Settings for email notifications
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=your_app_password

# Email Templates
EMAIL_FROM=<EMAIL>
EMAIL_FROM_NAME=WBS Aistech

# =============================================================================
# FILE UPLOAD CONFIGURATION
# =============================================================================
# Maximum file size in bytes (100MB)
MAX_FILE_SIZE=104857600

# Allowed file types
ALLOWED_FILE_TYPES=jpg,jpeg,png,gif,pdf,doc,docx,xls,xlsx,ppt,pptx

# Upload directory
UPLOAD_DIR=./uploads

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================
# Log levels: error, warn, info, debug
LOG_LEVEL=info
LOG_DIR=./logs

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================
# Rate limiting
RATE_LIMIT_TTL=60
RATE_LIMIT_LIMIT=100

# Session configuration
SESSION_SECRET=CHANGE_THIS_SESSION_SECRET
SESSION_MAX_AGE=86400000

# =============================================================================
# EXTERNAL SERVICES (Optional)
# =============================================================================
# N8N Webhook Configuration
N8N_WEBHOOK_URL=https://your-n8n-instance.com/webhook

# Third-party API keys
EXTERNAL_API_KEY=your_external_api_key

# =============================================================================
# MONITORING AND HEALTH CHECKS
# =============================================================================
# Health check configuration
HEALTH_CHECK_TIMEOUT=5000
HEALTH_CHECK_INTERVAL=30000

# =============================================================================
# SSL/TLS CONFIGURATION
# =============================================================================
# SSL certificate paths (if handling SSL in application)
SSL_CERT_PATH=/etc/ssl/certs/foobar.com.crt
SSL_KEY_PATH=/etc/ssl/private/foobar.com.key

# =============================================================================
# BACKUP CONFIGURATION
# =============================================================================
# Database backup settings
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30
BACKUP_S3_BUCKET=your-backup-bucket
BACKUP_S3_REGION=us-east-1
BACKUP_S3_ACCESS_KEY=your_s3_access_key
BACKUP_S3_SECRET_KEY=your_s3_secret_key

# =============================================================================
# PERFORMANCE CONFIGURATION
# =============================================================================
# Cache settings
CACHE_TTL=3600
CACHE_MAX_ITEMS=1000

# Database query timeout
DB_QUERY_TIMEOUT=30000

# =============================================================================
# FEATURE FLAGS
# =============================================================================
# Enable/disable features
FEATURE_CHAT_ENABLED=true
FEATURE_FILE_UPLOAD_ENABLED=true
FEATURE_EMAIL_NOTIFICATIONS=true
FEATURE_AUDIT_LOGGING=true

# =============================================================================
# DEVELOPMENT/DEBUG (Set to false in production)
# =============================================================================
DEBUG_MODE=false
ENABLE_SWAGGER=false
ENABLE_CORS_CREDENTIALS=false
