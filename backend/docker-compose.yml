version: '3.8'
services:
  csrit-app:
    image: ${IMAGE_APP_TAG}
    build: .
    networks:
      - app_network
    container_name: csirt-back
    stdin_open: true
    tty: true
    ports:
     - "3030:3000"
    volumes:
     - "csirt:/app/uploads"
    environment:
     - APP_NAME=CSIRT
     - APP_PORT=3000
     - APP_URL=https://csirt-stag.aistech.id
     - DB_HOST=**************
     - DB_PORT=5432
     - DB_USER=aistech
     - DB_PASS=PBLLAsqVT8tZ4k
     - DB_NAME=dbcsrit
     - DB_ENTITIES="['dist/**/*.entity{.ts,.js}']"
     - JWT_REFRESH_TOKEN_EXPIRATION_TIME=3600
     - JWT_REFRESH_TOKEN_SECRET=S3cretT3chN1que
     - JWT_ACCESS_TOKEN_EXPIRATION_TIME=7200 
     - JWT_ACCESS_TOKEN_SECRET=S3cretT3chN1que
     - JWT_ACCESS_COOKIE=true
     - HASH_SALT=20
     - SMTP_HOST=smtp.gmail.com
     - SMTP_USER=<EMAIL>
     - SMTP_PASS=dikdnvmzqueycyfp
     - SMTP_PORT=587
     - RECAPTCHA_SITE_KEY=6Lfl4MEoAAAAAIdX7I6zXMwfJcKUz9u-vBOn6QRF

    restart: always



volumes:
  csirt:
    driver: local
    
networks:
  app_network:
