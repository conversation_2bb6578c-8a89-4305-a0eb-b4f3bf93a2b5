require('dotenv').config();
const pgp = require('pg-promise')();
const db = pgp(
  `postgres://${process.env.DB_USER}:${process.env.DB_PASS}@${
    process.env.DB_HOST
  }:${parseInt(process.env.DB_PORT)}/${process.env.DB_NAME}`,
);

async function runSeedScripts() {
  try {
    const sqlScripts = ['ticket-status.seed.sql', 'role-seed.sql']; // Add the names of your SQL seed scripts here
    for (const script of sqlScripts) {
      const sql = require('fs').readFileSync(`./src/seeds/${script}`, 'utf8');
      await db.none(sql);
      console.log(`Seed script ${script} executed successfully.`);
    }
  } catch (error) {
    console.error('Error executing seed scripts:', error);
  } finally {
    pgp.end();
  }
}

runSeedScripts();
