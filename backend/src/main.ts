import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { join } from 'path';
import { NestExpressApplication } from '@nestjs/platform-express';
import * as cookieParser from 'cookie-parser';
import * as express from 'express';
import 'dotenv/config';
import {
  WinstonModule,
  utilities as nestWinstonModuleUtilities,
} from 'nest-winston';
import * as winston from 'winston';
import * as tsconfigPaths from 'tsconfig-paths';

async function bootstrap() {
  // TypeScript path mapping setup
  const baseUrl = './';
  const cleanup = tsconfigPaths.register({
    baseUrl,
    paths: {},
  });

  // Configure Winston logger
  const logger = WinstonModule.createLogger({
    transports: [
      new winston.transports.Console({
        format: winston.format.combine(
          winston.format.timestamp(),
          winston.format.colorize(),
          nestWinstonModuleUtilities.format.nestLike('CSRIT', {
            colors: true,
            prettyPrint: true,
          }),
        ),
      }),
    ],
  });

  // Create NestJS application
  const app = await NestFactory.create<NestExpressApplication>(AppModule, {
    logger,
  });

  // Set global API prefix for same-domain deployment
  app.setGlobalPrefix('api');

  // Configure CORS for same-domain deployment
  app.enableCors({
    origin:
      process.env.NODE_ENV === 'production'
        ? process.env.FRONTEND_URL || 'https://foobar.com'
        : true,
    methods: ['GET', 'HEAD', 'PUT', 'PATCH', 'POST', 'DELETE', 'OPTIONS'],
    allowedHeaders: [
      'content-type',
      'authorization',
      'x-csrf-token',
      'x-requested-with',
    ],
    credentials: true,
  });

  // Configure raw body parsing for webhook signature validation
  // This must be done before any other body parsing middleware
  app.use('/api/v1/n8n-hooks', express.raw({ type: 'application/json' }));
  app.use(
    express.json({
      verify: (req: any, res, buf) => {
        // Store raw body for signature validation
        req.rawBody = buf;
      },
    }),
  );

  // Cookie parser for JWT authentication
  app.use(cookieParser());

  // Trust proxy for secure cookies behind reverse proxy
  app.set('trust proxy', 1);

  // Start server
  const port = parseInt(process.env.APP_PORT) || 5000;
  await app.listen(port);

  logger.log(`🚀 Application is running on: http://localhost:${port}`);

  // Cleanup TypeScript paths
  cleanup();
}

bootstrap().catch((error) => {
  console.error('❌ Error starting server:', error);
  process.exit(1);
});
