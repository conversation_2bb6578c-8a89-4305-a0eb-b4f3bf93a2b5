import { Injectable, Logger, HttpException, HttpStatus } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { HttpService } from '@nestjs/axios';
import { firstValueFrom } from 'rxjs';
import * as crypto from 'crypto';

export interface N8nChatEvent {
  eventType:
    | 'new_chat'
    | 'new_message'
    | 'chat_transferred'
    | 'chat_closed'
    | 'user_escalation_request';
  chatRoomId: string;
  userId?: string; // ID of the end-user
  operatorId?: string; // ID of the operator, if applicable
  message?: {
    id: string; // Message ID from local DB
    content: string; // Plain text content of the message
    sender: 'user' | 'operator'; // Who sent this message being relayed
    timestamp: string; // ISO 8601 string format of when the original message was sent/created
  };
  metadata?: Record<string, any>; // Any additional contextual data
  timestamp: string; // ISO 8601 string, when this event object was created by NestJS
  source: 'nestjs-chat'; // Identifier for the source system
}

export interface N8nBotResponse {
  success: boolean;
  reason?:
    | 'N8N_NOT_CONFIGURED'
    | 'N8N_REQUEST_FAILED'
    | 'N8N_INVALID_RESPONSE'
    | string; // For diagnostics
  messageId?: string; // If n8n created a message, its ID in n8n (optional)
  content?: string; // If n8n wants to reply directly via this sync call (less common for webhooks)
  actions?: Array<{
    // Actions n8n might suggest NestJS to take (optional)
    type:
      | 'transfer_to_operator'
      | 'close_chat'
      | 'escalate'
      | 'send_quick_reply';
    data?: Record<string, any>;
  }>;
  error?: string; // Error message if success is false
}

@Injectable()
export class N8nIntegrationService {
  private readonly logger = new Logger(N8nIntegrationService.name);
  private readonly isN8nEnabled: boolean;
  private readonly n8nWebhookUrl: string;
  private readonly n8nOutgoingApiKey: string;
  private readonly n8nIncomingWebhookSecret: string;

  constructor(
    private configService: ConfigService,
    private httpService: HttpService,
  ) {
    this.isN8nEnabled = this.configService.get<boolean>(
      'ENABLE_N8N_INTEGRATION',
      false,
    );
    this.n8nWebhookUrl = this.configService.get<string>('N8N_WEBHOOK_URL');
    this.n8nOutgoingApiKey = this.configService.get<string>(
      'N8N_OUTGOING_API_KEY',
    );
    this.n8nIncomingWebhookSecret = this.configService.get<string>(
      'N8N_INCOMING_WEBHOOK_SECRET',
    );

    if (this.isN8nEnabled && !this.n8nWebhookUrl) {
      this.logger.error(
        'N8N_INTEGRATION is ENABLED but N8N_WEBHOOK_URL is not configured. N8N calls will fail.',
      );
    } else if (this.isN8nEnabled) {
      this.logger.log(
        `N8N Integration Service initialized. Enabled: ${this.isN8nEnabled}, Webhook URL: ${this.n8nWebhookUrl}`,
      );
    } else {
      this.logger.log(
        'N8N Integration Service initialized. N8N Integration is DISABLED.',
      );
    }
  }

  /**
   * Send a chat-related event to the configured N8N webhook.
   * This method handles the actual HTTP POST request to N8N.
   */
  async sendChatEvent(
    eventData: Omit<N8nChatEvent, 'timestamp' | 'source'>,
  ): Promise<N8nBotResponse> {
    if (!this.isN8nEnabled || !this.n8nWebhookUrl) {
      const reason = this.isN8nEnabled
        ? 'N8N_WEBHOOK_URL_NOT_CONFIGURED'
        : 'N8N_DISABLED';
      this.logger.warn(
        `Skipping N8N event (${eventData.eventType}) because: ${reason}. Chat flow continues locally.`,
      );
      return { success: false, reason };
    }

    const payload: N8nChatEvent = {
      ...eventData,
      timestamp: new Date().toISOString(),
      source: 'nestjs-chat',
    };

    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
    };
    if (this.n8nOutgoingApiKey) {
      headers['X-API-Key'] = this.n8nOutgoingApiKey;
    }

    this.logger.debug(
      `Sending event to N8N (${this.n8nWebhookUrl}): ${payload.eventType}`,
      payload,
    );

    try {
      const response = await firstValueFrom(
        this.httpService.post(this.n8nWebhookUrl, payload, {
          headers,
          timeout: 5000,
        }),
      );

      this.logger.log(
        `N8N response received for ${payload.eventType} (Status: ${response.status}):`,
        response.data,
      );

      if (response.status >= 200 && response.status < 300) {
        return {
          success: true,
          ...(typeof response.data === 'object'
            ? response.data
            : { rawResponse: response.data }),
        };
      } else {
        this.logger.error(
          `N8N returned non-success status ${response.status} for ${payload.eventType}. Response: ${JSON.stringify(response.data)}`,
        );
        return {
          success: false,
          reason: `N8N_HTTP_ERROR_${response.status}`,
          error: JSON.stringify(response.data),
        };
      }
    } catch (error) {
      const errorMessage = error.response?.data || error.message;
      this.logger.error(
        `Failed to send event to N8N (${payload.eventType}): ${errorMessage}`,
        error.stack,
      );
      if (error.isAxiosError && error.response) {
        this.logger.error('N8N error response details:', error.response.data);
        return {
          success: false,
          reason: 'N8N_REQUEST_FAILED',
          error: JSON.stringify(error.response.data),
        };
      }
      return {
        success: false,
        reason: 'N8N_REQUEST_FAILED',
        error: error.message,
      };
    }
  }

  // --- Specific Event Handler Methods ---

  async handleNewChat(
    chatRoomId: string,
    userId: string,
    initialMessage?: string,
  ): Promise<N8nBotResponse> {
    return this.sendChatEvent({
      eventType: 'new_chat',
      chatRoomId,
      userId,
      metadata: { initialMessage },
    });
  }

  async handleNewMessage(
    chatRoomId: string,
    messageId: string,
    content: string,
    senderId: string,
    senderType: 'user' | 'operator',
    originalTimestamp: string,
  ): Promise<N8nBotResponse> {
    return this.sendChatEvent({
      eventType: 'new_message',
      chatRoomId,
      userId: senderType === 'user' ? senderId : undefined,
      operatorId: senderType === 'operator' ? senderId : undefined,
      message: {
        id: messageId,
        content,
        sender: senderType,
        timestamp: originalTimestamp,
      },
    });
  }

  async handleChatTransferToOperator(
    chatRoomId: string,
    operatorId: string,
    reason?: string,
    transferredByUserId?: string,
  ): Promise<N8nBotResponse> {
    return this.sendChatEvent({
      eventType: 'chat_transferred',
      chatRoomId,
      operatorId,
      userId: transferredByUserId,
      metadata: { reason, transferType: 'bot_to_operator' },
    });
  }

  async handleUserEscalationRequest(
    chatRoomId: string,
    userId: string,
    messageContent?: string,
  ): Promise<N8nBotResponse> {
    return this.sendChatEvent({
      eventType: 'user_escalation_request',
      chatRoomId,
      userId,
      metadata: { messageContent },
    });
  }

  async handleChatClosure(
    chatRoomId: string,
    closedBy: 'user' | 'operator' | 'system' | 'bot',
    closerId?: string,
  ): Promise<N8nBotResponse> {
    return this.sendChatEvent({
      eventType: 'chat_closed',
      chatRoomId,
      userId: closedBy === 'user' && closerId ? closerId : undefined,
      operatorId: closedBy === 'operator' && closerId ? closerId : undefined,
      metadata: { closedBy },
    });
  }

  /**
   * Validates an incoming webhook signature from N8N.
   * IMPORTANT: 'payloadString' must be the raw, unparsed request body string.
   */
  validateIncomingWebhookSignature(
    payloadString: string,
    signatureFromHeader: string,
  ): boolean {
    if (!this.n8nIncomingWebhookSecret) {
      this.logger.warn(
        'N8N_INCOMING_WEBHOOK_SECRET not configured. Skipping signature validation. THIS IS INSECURE.',
      );
      return true;
    }
    if (!signatureFromHeader) {
      this.logger.warn('Missing signature in incoming N8N webhook header.');
      return false;
    }

    try {
      const expectedSignature = crypto
        .createHmac('sha256', this.n8nIncomingWebhookSecret)
        .update(payloadString)
        .digest('hex');

      const isValid = crypto.timingSafeEqual(
        Buffer.from(signatureFromHeader),
        Buffer.from(expectedSignature),
      );
      if (!isValid) {
        this.logger.warn(
          `Invalid N8N webhook signature. Expected: ${expectedSignature}, Got: ${signatureFromHeader}`,
        );
      }
      return isValid;
    } catch (error) {
      this.logger.error(
        'Error validating N8N incoming webhook signature:',
        error.message,
        error.stack,
      );
      return false;
    }
  }
}
