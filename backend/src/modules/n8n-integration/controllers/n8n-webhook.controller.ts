import {
  Controller,
  Post,
  Body,
  HttpException,
  HttpStatus,
  Logger,
  UseGuards,
} from '@nestjs/common';
import { N8nIntegrationService } from '../n8n-integration.service';
import { ChatService } from '../../chat/services/chat.service';
import { N8nAuthGuard } from '../guards/n8n-auth.guard';
import { ConfigService } from '@nestjs/config';

// DTO for N8N sending a message to a chat room
export interface N8nBotMessageDto {
  chatRoomId: string; // Target chat room
  content: string; // Message content (plain text)
  messageType?: 'text' | 'quick_reply' | 'card' | string; // Type of message, defaults to 'text'
  metadata?: {
    // For rich messages
    quickReplies?: Array<{ title: string; payload: string }>;
    cards?: Array<{
      title: string;
      subtitle?: string;
      imageUrl?: string;
      buttons?: Array<{
        title: string;
        type: 'postback' | 'web_url';
        payload?: string;
        url?: string;
      }>;
    }>;
    // Other metadata N8N might send
  };
  // Optional actions N8N might want NestJS to perform after sending the message
  actions?: Array<{
    type: 'transfer_to_operator' | 'close_chat' | 'request_feedback' | string; // Extensible
    data?: Record<string, any>; // Action-specific data
  }>;
}

// DTO for N8N triggering a specific action on a chat room
export interface N8nChatActionDto {
  chatRoomId: string;
  action: 'close_chat' | 'transfer_to_operator' | 'mark_resolved' | string; // Extensible
  reason?: string; // Reason for the action
  metadata?: Record<string, any>; // Additional context for the action
  // Example: for transfer_to_operator, metadata could include preferred_skill, original_query
}

@Controller('v1/n8n-hooks') // Versioned API endpoint
@UseGuards(N8nAuthGuard) // Secure all routes in this controller
export class N8nWebhookController {
  private readonly logger = new Logger(N8nWebhookController.name);

  constructor(
    private chatService: ChatService,
    private configService: ConfigService,
  ) {}

  /**
   * Endpoint for N8N to send a message from the bot to a specific chat room.
   */
  @Post('bot-message')
  async handleBotMessageFromN8n(
    @Body() messageDto: N8nBotMessageDto,
  ): Promise<any> {
    this.logger.log(
      `Received bot message from N8N for chatRoomId: ${messageDto.chatRoomId}. Content: "${messageDto.content.substring(0, 50)}..."`,
    );
    try {
      const chatRoom = await this.chatService.getChatRoom(
        messageDto.chatRoomId,
      );
      if (!chatRoom) {
        this.logger.warn(
          `Chat room ${messageDto.chatRoomId} not found for N8N bot message.`,
        );
        throw new HttpException('Chat room not found', HttpStatus.NOT_FOUND);
      }

      // Send bot message using ChatService
      const sentMessage = await this.chatService.sendBotMessage({
        chat_room_id: messageDto.chatRoomId,
        content: messageDto.content,
        message_type: messageDto.messageType || 'text',
        metadata: messageDto.metadata,
      });
      this.logger.log(
        `Bot message ${sentMessage.id} successfully processed and sent to room ${messageDto.chatRoomId}.`,
      );

      // Process any subsequent actions requested by N8N in the same payload
      if (messageDto.actions && messageDto.actions.length > 0) {
        this.logger.log(
          `Processing ${messageDto.actions.length} actions from N8N for chat ${messageDto.chatRoomId}.`,
        );
        await this.processN8nActions(
          messageDto.chatRoomId,
          'system_n8n_bot_message',
          messageDto.actions,
        );
      }

      return {
        success: true,
        messageId: sentMessage.id,
        timestamp: sentMessage.created_at,
      };
    } catch (error) {
      this.logger.error(
        `Error handling bot message from N8N for chat ${messageDto.chatRoomId}: ${error.message}`,
        error.stack,
      );
      // Avoid re-throwing generic HttpException if error is already one
      if (error instanceof HttpException) throw error;
      throw new HttpException(
        'Failed to process bot message',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Endpoint for N8N to trigger specific actions on a chat room (e.g., close chat, transfer to operator).
   */
  @Post('chat-action')
  async handleChatActionFromN8n(
    @Body() actionDto: N8nChatActionDto,
  ): Promise<any> {
    this.logger.log(
      `Received chat action from N8N: ${actionDto.action} for chatRoomId: ${actionDto.chatRoomId}. Reason: ${actionDto.reason || 'N/A'}`,
    );
    try {
      await this.processN8nActions(actionDto.chatRoomId, 'system_n8n_action', [
        {
          type: actionDto.action,
          reason: actionDto.reason,
          data: actionDto.metadata,
        },
      ]); // Wrap single action in array with proper structure
      return {
        success: true,
        action: actionDto.action,
        chatRoomId: actionDto.chatRoomId,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      this.logger.error(
        `Error handling chat action from N8N for chat ${actionDto.chatRoomId}, action ${actionDto.action}: ${error.message}`,
        error.stack,
      );
      if (error instanceof HttpException) throw error;
      throw new HttpException(
        'Failed to process chat action',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Centralized processing of actions requested by N8N.
   */
  private async processN8nActions(
    chatRoomId: string,
    initiatorId: string, // e.g., 'system_n8n_bot_message', 'system_n8n_action'
    actions: Array<{
      type: string;
      data?: Record<string, any>;
      reason?: string;
    }>,
  ): Promise<void> {
    for (const action of actions) {
      this.logger.log(
        `Processing N8N action: ${action.type} for chatRoomId: ${chatRoomId}`,
      );
      switch (action.type) {
        case 'transfer_to_operator':
          // The 'reason' for transfer can come from action.data.reason or action.reason
          const transferReason =
            action.data?.reason ||
            action.reason ||
            'Transfer requested by N8N bot';
          const transferResult =
            await this.chatService.requestOperatorEscalation(
              chatRoomId,
              initiatorId,
              transferReason,
            );
          if (!transferResult.success) {
            this.logger.warn(
              `N8N requested operator transfer for chat ${chatRoomId}, but it failed: ${transferResult.message}`,
            );
            // Optionally, notify N8N back if the transfer failed (e.g., via another webhook call if critical)
          }
          break;
        case 'close_chat':
          const closeReason =
            action.data?.reason || action.reason || 'Chat closed by N8N bot';
          await this.chatService.closeChatRoomByBot(chatRoomId, closeReason);
          break;
        case 'mark_resolved':
          const resolvedReason =
            action.data?.reason ||
            action.reason ||
            'Chat marked as resolved by N8N bot';
          await this.chatService.markChatResolved(chatRoomId, resolvedReason);
          break;
        // Add cases for 'request_feedback' or other custom actions if needed
        default:
          this.logger.warn(
            `Received unknown N8N action type: ${action.type} for chat ${chatRoomId}.`,
          );
          // Optionally, throw an error or log more details
          // throw new HttpException(`Unsupported N8N action type: ${action.type}`, HttpStatus.BAD_REQUEST);
          break;
      }
    }
  }

  /**
   * Health check endpoint for N8N to verify connectivity.
   */
  @Post('health-check')
  async n8nHealthCheck(): Promise<any> {
    this.logger.log('N8N health check endpoint called.');
    return {
      status: 'NestJS N8N Integration Endpoint is Healthy',
      timestamp: new Date().toISOString(),
    };
  }
}
