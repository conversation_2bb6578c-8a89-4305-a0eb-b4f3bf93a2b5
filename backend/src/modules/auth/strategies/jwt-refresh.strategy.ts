import { ExtractJwt, Strategy } from 'passport-jwt';
import { PassportStrategy } from '@nestjs/passport';
import { ForbiddenException, Injectable } from '@nestjs/common';
import { Request } from 'express';
import { UserService } from '../../user/user.service';
import { isCookieJwt } from 'src/utils';
// import TokenPayload from './tokenPayload.interface';
@Injectable()
export class JwtRefreshStrategy extends PassportStrategy(
    Strategy,
    'jwt-refresh'
) {
    constructor(
        private readonly userService: UserService,
    ) {
        super({
        // jwtFromRequest: ExtractJwt.fromExtractors([(request: Request) => {
        //     return request?.cookies?.Refresh;
        // }]),
        jwtFromRequest: isCookieJwt()?ExtractJwt.fromExtractors([(request: Request) => {
                return request?.cookies?.Refresh;
            }]):ExtractJwt.fromAuthHeaderAsBearerToken(),
            secretOrKey: process.env.JWT_REFRESH_TOKEN_SECRET,
            passReqToCallback: true,
        });
    }
    async validate(request: Request, user:TokenPayload) {
        const refreshToken = this.getRefreshToken(request); 
        if (!refreshToken) throw new ForbiddenException('Refresh token tidak ditemukan');

        return {
            ...user,
            refreshToken
        };
        // return this.userService.getUserIfRefreshTokenMatches(refreshToken, payload.id);
    }

    getRefreshToken(request) {
        if(isCookieJwt()) {
            return request.cookies?.Refresh;
        }
        return request
        ?.get('Authorization')
        ?.replace('Bearer', '')
        .trim();
    }
}