import {
  <PERSON>,
  Get,
  Post,
  Body,
  Param,
  UploadedFile,
  UseInterceptors,
  Req,
  BadRequestException,
} from '@nestjs/common';
import { UserService } from './user.service';
import { UpdateUserDto } from './dto/update-user.dto';
import { ApiBody, ApiConsumes, ApiTags } from '@nestjs/swagger';
import { FileInterceptor } from '@nestjs/platform-express';
import { createWriteStream } from 'fs';
import * as fs from 'fs';
import { Public } from 'src/common/decorators';
import { IsRole } from 'src/common/decorators/role.decorator';
import { StorageConfig } from 'src/config/storage.config';

@ApiTags('Modul User')
@Controller('v1/user')
export class UserController {
  constructor(private readonly userService: UserService) {}

  @Post('/edit/:id_user')
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        nama: { type: 'string' },
        roleId: { type: 'string' },
        username: { type: 'string' },
        email: { type: 'string' },
        foto: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  @UseInterceptors(FileInterceptor('foto'))
  async updateUser(
    @Param('id_user') id: string,
    @Body() body,
    @UploadedFile() foto: Express.Multer.File,
  ) {
    try {
      let namaFile;

      if (foto) {
        // Validate file size
        if (foto.size > StorageConfig.MAX_FILE_SIZE) {
          throw new BadRequestException(
            'File foto terlalu besar. Maksimal 10MB',
          );
        }

        // Validate file extension
        const extension = foto.originalname.substring(
          foto.originalname.lastIndexOf('.'),
        );
        if (
          !StorageConfig.ALLOWED_EXTENSIONS.includes(extension.toLowerCase())
        ) {
          throw new BadRequestException('Tipe file foto tidak diizinkan');
        }

        // Validate MIME type for images
        const allowedImageMimes = ['image/jpeg', 'image/jpg', 'image/png'];
        if (!allowedImageMimes.includes(foto.mimetype)) {
          throw new BadRequestException('Tipe file foto tidak valid');
        }

        // Ensure secure upload directory exists
        if (!fs.existsSync(StorageConfig.SECURE_UPLOAD_PATH)) {
          fs.mkdirSync(StorageConfig.SECURE_UPLOAD_PATH, { recursive: true });
        }

        // Generate secure filename
        namaFile = StorageConfig.generateSecureFilename(foto.originalname);
        const secureFilePath = StorageConfig.getSecureFilePath(namaFile);

        // Write file to secure location
        const ws = createWriteStream(secureFilePath);
        ws.write(foto.buffer);
      }

      const result = await this.userService.updateUser(id, body, namaFile);
      return {
        result,
      };
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new BadRequestException('Data User Gagal Di Update');
    }
  }

  @Get()
  // @Public()
  @IsRole('admin')
  //@UseInterceptors(CacheInterceptor)
  async findAll() {
    const data = await this.userService.findAll();
    return {
      success: true,
      message: 'Data User',
      data: data,
    };
  }

  @Get('user-activity')
  @Public()
  async findAllUserAc() {
    const data = await this.userService.findAllUserAc();
    return {
      success: true,
      message: 'Data User Activity',
      data: data,
    };
  }

  @Get(':id')
  //@UseInterceptors(CacheInterceptor)
  async findOne(@Param('id') id: string) {
    if (id == null)
      return {
        success: false,
        message: 'error id',
      };
    const data = await this.userService.findByIdUser(id);
    return {
      success: true,
      message: 'Data User',
      data: data,
      // akses: data.akses,
    };
  }

  @Get('used/email/:email')
  @Public()
  //@UseInterceptors(CacheInterceptor)
  async checkEmail(@Param('email') email: string) {
    const data = await this.userService.checkEmail(email);
    return {
      data,
      // akses: data.akses,
    };
  }

  @Post(':id')
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        nama: { type: 'string' },
        // username: { type: 'string' },
        // password: { type: 'string' },
        // email: { type: 'string' },
        id_menu: { type: 'array' },
        foto: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  @UseInterceptors(FileInterceptor('foto'))
  async update(
    @Param('id') id: string,
    @Body() body,
    @UploadedFile() foto: Express.Multer.File,
  ) {
    try {
      let namaFile;

      if (foto) {
        // Validate file size
        if (foto.size > StorageConfig.MAX_FILE_SIZE) {
          throw new BadRequestException(
            'File foto terlalu besar. Maksimal 10MB',
          );
        }

        // Validate file extension
        const extension = foto.originalname.substring(
          foto.originalname.lastIndexOf('.'),
        );
        if (
          !StorageConfig.ALLOWED_EXTENSIONS.includes(extension.toLowerCase())
        ) {
          throw new BadRequestException('Tipe file foto tidak diizinkan');
        }

        // Validate MIME type for images
        const allowedImageMimes = ['image/jpeg', 'image/jpg', 'image/png'];
        if (!allowedImageMimes.includes(foto.mimetype)) {
          throw new BadRequestException('Tipe file foto tidak valid');
        }

        // Ensure secure upload directory exists
        if (!fs.existsSync(StorageConfig.SECURE_UPLOAD_PATH)) {
          fs.mkdirSync(StorageConfig.SECURE_UPLOAD_PATH, { recursive: true });
        }

        // Generate secure filename
        namaFile = StorageConfig.generateSecureFilename(foto.originalname);
        const secureFilePath = StorageConfig.getSecureFilePath(namaFile);

        // Write file to secure location
        const ws = createWriteStream(secureFilePath);
        ws.write(foto.buffer);
      }

      const result = await this.userService.update(id, body, namaFile);
      return {
        success: true,
        message: 'Data User Berhasil Di Update',
      };
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new BadRequestException('Data User Gagal Di Update');
    }
  }

  @Post('refreshToken/:id')
  updateToken(@Param('id') id: string, @Body() updateUserDto: UpdateUserDto) {
    return this.userService.updateToken(id, updateUserDto);
  }

  @Post('delete/:id')
  //@UseInterceptors(CacheInterceptor)
  async remove(@Param('id') id: string) {
    const data = await this.userService.remove(id);
    return {
      success: true,
      message: 'Data User Berhasil Di Hapus',
      data: data,
    };
  }
}
