import {
  Injectable,
  NotFoundException,
  BadRequestException,
  ConflictException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ChatUser } from '../entities/user.entity';
import { ChatRoom } from '../entities/chat-room.entity';
import { Message } from '../entities/message.entity';
import { ChatRoomParticipant } from '../entities/chat-room-participant.entity';
import { ChatOperator } from '../entities/operator.entity';
import { User } from '../../user/entities/user.entity';
import { EncryptionService } from './encryption.service';
import * as bcrypt from 'bcrypt';

interface SendMessageParams {
  chatRoomId: string;
  content: string;
  senderId: string;
  senderType: 'guest_user' | 'system_user';
  messageType?: string;
  attachmentUrl?: string;
  attachmentName?: string;
  attachmentSize?: number;
  replyToMessageId?: string;
}

@Injectable()
export class ChatService {
  constructor(
    @InjectRepository(ChatUser)
    private readonly chatUserRepository: Repository<ChatUser>,
    @InjectRepository(ChatOperator)
    private readonly chatOperatorRepository: Repository<ChatOperator>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(ChatRoom)
    private readonly chatRoomRepository: Repository<ChatRoom>,
    @InjectRepository(Message)
    private readonly messageRepository: Repository<Message>,
    @InjectRepository(ChatRoomParticipant)
    private readonly chatRoomParticipantRepository: Repository<ChatRoomParticipant>,
    private readonly encryptionService: EncryptionService,
  ) {}

  // Operator authentication methods (users don't need authentication for public chat)
  async validateOperator(
    username: string,
    password: string,
  ): Promise<ChatOperator | null> {
    const operator = await this.chatOperatorRepository.findOne({
      where: { username },
    });

    if (operator && (await bcrypt.compare(password, operator.password_hash))) {
      return operator;
    }

    return null;
  }

  async createOperator(operatorData: {
    username: string;
    password: string;
    name?: string;
    email?: string;
    role?: string;
  }): Promise<ChatOperator> {
    // Check if operator already exists
    const existingOperator = await this.chatOperatorRepository.findOne({
      where: [
        { username: operatorData.username },
        ...(operatorData.email ? [{ email: operatorData.email }] : []),
      ],
    });

    if (existingOperator) {
      throw new ConflictException(
        'Operator with this username or email already exists',
      );
    }

    // Hash password
    const saltRounds = 10;
    const hashedPassword = await bcrypt.hash(operatorData.password, saltRounds);

    const operator = this.chatOperatorRepository.create({
      username: operatorData.username,
      password_hash: hashedPassword,
      name: operatorData.name || operatorData.username,
      email: operatorData.email,
      role: operatorData.role || 'operator',
      is_active: true,
    });

    return await this.chatOperatorRepository.save(operator);
  }

  // Create guest user for public chat (no password required)
  async createGuestUser(userData: {
    email?: string;
    phone?: string;
    name: string;
  }): Promise<ChatUser> {
    // Check if user already exists by email or phone
    let existingUser = null;
    if (userData.email || userData.phone) {
      existingUser = await this.chatUserRepository.findOne({
        where: [
          ...(userData.email ? [{ email: userData.email }] : []),
          ...(userData.phone ? [{ phone: userData.phone }] : []),
        ],
      });
    }

    if (existingUser) {
      // Update existing user's name and return
      existingUser.name = userData.name;
      return await this.chatUserRepository.save(existingUser);
    }

    // Create new guest user without password
    const user = this.chatUserRepository.create({
      email: userData.email || `guest_${Date.now()}@temp.com`,
      phone: userData.phone,
      name: userData.name,
      password_hash: '', // Empty password for guest users
      verification_status: 'guest',
    });

    return await this.chatUserRepository.save(user);
  }

  // Chat room management
  async initiateChatSession(userData: {
    email?: string;
    phone?: string;
    name: string;
    initialMessage?: string;
  }): Promise<{ user: ChatUser; chatRoom: ChatRoom; messageId?: string }> {
    // Create or get existing guest user
    const user = await this.createGuestUser(userData);

    // Create chat room
    const chatRoom = await this.createChatRoom(user.id);

    let messageId;
    if (userData.initialMessage) {
      const message = await this.sendMessage({
        chatRoomId: chatRoom.id,
        content: userData.initialMessage,
        senderId: user.id,
        senderType: 'guest_user',
      });
      messageId = message.id;
    }

    return { user, chatRoom, messageId };
  }

  async createChatRoom(userId: string): Promise<ChatRoom> {
    const chatRoom = this.chatRoomRepository.create({
      type: 'user-operator',
      status: 'active',
    });

    const savedRoom = await this.chatRoomRepository.save(chatRoom);

    // Add user as participant
    await this.addParticipantToRoom(savedRoom.id, userId, 'guest_user');

    return savedRoom;
  }

  async addParticipantToRoom(
    chatRoomId: string,
    participantId: string,
    participantType: 'guest_user' | 'system_user',
  ): Promise<ChatRoomParticipant> {
    const participant = this.chatRoomParticipantRepository.create({
      chat_room: { id: chatRoomId } as ChatRoom,
      participant_type: participantType,
      status: 'active',
    });

    if (participantType === 'guest_user') {
      participant.guest_user = { id: participantId } as ChatUser;
    } else {
      participant.system_user = { id: participantId } as User;
    }

    return await this.chatRoomParticipantRepository.save(participant);
  }

  async getUserChatRooms(
    userId: string,
    userType: 'guest_user' | 'system_user',
  ): Promise<ChatRoom[]> {
    const queryBuilder = this.chatRoomRepository
      .createQueryBuilder('room')
      .leftJoinAndSelect('room.participants', 'participant')
      .leftJoinAndSelect('participant.guest_user', 'guest_user')
      .leftJoinAndSelect('participant.system_user', 'system_user');

    if (userType === 'guest_user') {
      queryBuilder.where('guest_user.id = :userId', { userId });
    } else {
      queryBuilder.where('system_user.id = :userId', { userId });
    }

    return await queryBuilder.getMany();
  }

  async userHasAccessToRoom(
    userId: string,
    userType: 'guest_user' | 'system_user',
    chatRoomId: string,
  ): Promise<boolean> {
    const participant = await this.chatRoomParticipantRepository
      .createQueryBuilder('participant')
      .leftJoinAndSelect('participant.guest_user', 'guest_user')
      .leftJoinAndSelect('participant.system_user', 'system_user')
      .where('participant.chat_room.id = :chatRoomId', { chatRoomId })
      .andWhere('participant.status = :status', { status: 'active' })
      .getMany();

    return participant.some((p) => {
      if (userType === 'guest_user' && p.guest_user) {
        return p.guest_user.id === userId;
      }
      if (userType === 'system_user' && p.system_user) {
        return p.system_user.id === userId;
      }
      return false;
    });
  }

  // Message management
  async sendMessage(params: SendMessageParams): Promise<Message> {
    const chatRoom = await this.chatRoomRepository.findOne({
      where: { id: params.chatRoomId },
    });

    if (!chatRoom) {
      throw new NotFoundException('Chat room not found');
    }

    // Encrypt message content
    const encryptedContent = this.encryptionService.encrypt(params.content);

    const message = this.messageRepository.create({
      chat_room: chatRoom,
      sender_type: params.senderType,
      content: encryptedContent,
      message_type: params.messageType || 'text',
      attachment_url: params.attachmentUrl,
      attachment_name: params.attachmentName,
      attachment_size: params.attachmentSize,
      reply_to_message_id: params.replyToMessageId,
      status: 'sent',
    });

    if (params.senderType === 'guest_user') {
      message.sender_guest_user = { id: params.senderId } as ChatUser;
    } else {
      message.sender_system_user = { id: params.senderId } as User;
    }

    const savedMessage = await this.messageRepository.save(message);

    // Decrypt content for return (for immediate display)
    savedMessage.content = params.content;

    return savedMessage;
  }

  async getChatRoomMessages(
    chatRoomId: string,
    page: number = 1,
    limit: number = 50,
  ): Promise<{ messages: Message[]; total: number }> {
    const [messages, total] = await this.messageRepository.findAndCount({
      where: {
        chat_room: { id: chatRoomId },
        is_deleted: false,
      },
      relations: ['sender_guest_user', 'sender_system_user'],
      order: { timestamp: 'DESC' },
      skip: (page - 1) * limit,
      take: limit,
    });

    // Decrypt message content
    const decryptedMessages = messages.map((message) => {
      message.content = this.encryptionService.decrypt(message.content);
      return message;
    });

    return {
      messages: decryptedMessages,
      total,
    };
  }

  async updateChatRoomLastMessage(chatRoomId: string): Promise<void> {
    await this.chatRoomRepository.update(chatRoomId, {
      last_message_at: new Date(),
    });
  }

  async markMessageAsRead(
    chatRoomId: string,
    messageId: string,
    userId: string,
    userType: 'guest_user' | 'system_user',
  ): Promise<void> {
    // Update participant's last read message
    const queryBuilder = this.chatRoomParticipantRepository
      .createQueryBuilder('participant')
      .leftJoinAndSelect('participant.guest_user', 'guest_user')
      .leftJoinAndSelect('participant.system_user', 'system_user')
      .where('participant.chat_room.id = :chatRoomId', { chatRoomId });

    if (userType === 'guest_user') {
      queryBuilder.andWhere('guest_user.id = :userId', { userId });
    } else {
      queryBuilder.andWhere('system_user.id = :userId', { userId });
    }

    const participant = await queryBuilder.getOne();

    if (participant) {
      await this.chatRoomParticipantRepository.update(participant.id, {
        last_read_message_id: messageId,
      });
    }
  }

  // CMS/Admin methods
  async getActiveChatRooms(): Promise<ChatRoom[]> {
    return await this.chatRoomRepository.find({
      where: { status: 'active' },
      relations: [
        'participants',
        'participants.guest_user',
        'participants.system_user',
      ],
      order: { last_message_at: 'DESC' },
    });
  }

  async assignOperatorToRoom(
    chatRoomId: string,
    operatorId: string,
  ): Promise<void> {
    // Check if operator is already in the room
    const existingParticipant =
      await this.chatRoomParticipantRepository.findOne({
        where: {
          chat_room: { id: chatRoomId },
          system_user: { id: operatorId },
        },
      });

    if (!existingParticipant) {
      await this.addParticipantToRoom(chatRoomId, operatorId, 'system_user');
    }
  }

  async getOnlineOperators(): Promise<User[]> {
    // Get users with chat operator role who are currently online
    // This would need to be implemented based on your role system
    return await this.userRepository.find({
      where: {
        status: 1, // Active users
        // Add role condition here based on your role system
      },
      relations: ['role'],
    });
  }

  async updateOperatorOnlineStatus(
    operatorId: string,
    isOnline: boolean,
  ): Promise<void> {
    // Update last seen for system users
    await this.userRepository.update(operatorId, {
      updated_at: new Date(),
    });
  }

  // Additional methods for N8N integration

  /**
   * Get a chat room by ID
   */
  async getChatRoom(chatRoomId: string): Promise<ChatRoom | null> {
    return await this.chatRoomRepository.findOne({
      where: { id: chatRoomId },
      relations: [
        'participants',
        'participants.guest_user',
        'participants.system_user',
      ],
    });
  }

  /**
   * Send a message as bot (for N8N integration)
   */
  async sendBotMessage(dto: {
    chat_room_id: string;
    content: string;
    message_type?: string;
    metadata?: any;
  }): Promise<Message> {
    const chatRoom = await this.chatRoomRepository.findOne({
      where: { id: dto.chat_room_id },
    });

    if (!chatRoom) {
      throw new NotFoundException('Chat room not found for bot message');
    }

    // Encrypt message content
    const encryptedContent = this.encryptionService.encrypt(dto.content);

    const botMessage = this.messageRepository.create({
      chat_room: chatRoom,
      sender_type: 'system', // Bot messages are system messages
      content: encryptedContent,
      message_type: dto.message_type || 'text',
      metadata: dto.metadata ? JSON.stringify(dto.metadata) : null,
      status: 'sent',
    });

    const savedMessage = await this.messageRepository.save(botMessage);

    // Update chat room last message time
    await this.updateChatRoomLastMessage(dto.chat_room_id);

    // Decrypt content for return (for immediate display)
    savedMessage.content = dto.content;

    return savedMessage;
  }

  /**
   * Request operator escalation from bot to human operator
   */
  async requestOperatorEscalation(
    chatRoomId: string,
    userId: string,
    reasonMessage?: string,
  ): Promise<{
    success: boolean;
    message: string;
    operatorAssigned: boolean;
    assignedOperatorId?: string;
  }> {
    const chatRoom = await this.chatRoomRepository.findOne({
      where: { id: chatRoomId },
    });

    if (!chatRoom) {
      throw new NotFoundException('Chat room not found');
    }

    if (chatRoom.current_handler === 'operator') {
      return {
        success: false,
        message: 'This chat is already being handled by an operator.',
        operatorAssigned: true,
      };
    }

    // Get available operators
    const availableOperators = await this.getOnlineOperators();

    if (availableOperators.length === 0) {
      const noOperatorMsg =
        'Maaf, saat ini tidak ada operator yang tersedia. Anda dapat melanjutkan percakapan dengan chatbot atau mencoba lagi nanti.';

      // Send system message to inform user
      await this.sendSystemMessage(chatRoomId, noOperatorMsg);

      return {
        success: false,
        message: noOperatorMsg,
        operatorAssigned: false,
      };
    }

    // Assign first available operator
    const assignedOperator = availableOperators[0];

    // Update chat room handler
    await this.chatRoomRepository.update(chatRoomId, {
      current_handler: 'operator',
      handler_assigned_at: new Date(),
    });

    // Add operator as participant if not already
    await this.assignOperatorToRoom(chatRoomId, assignedOperator.id);

    // Send welcome message from operator
    const welcomeMsg = `Halo! Saya akan membantu Anda. Ada yang bisa saya bantu?`;
    await this.sendSystemMessage(chatRoomId, welcomeMsg);

    return {
      success: true,
      message: `Anda telah terhubung dengan operator.`,
      operatorAssigned: true,
      assignedOperatorId: assignedOperator.id,
    };
  }

  /**
   * Close chat room by bot
   */
  async closeChatRoomByBot(
    chatRoomId: string,
    reason: string,
  ): Promise<ChatRoom> {
    const chatRoom = await this.chatRoomRepository.findOne({
      where: { id: chatRoomId },
    });

    if (!chatRoom) {
      throw new NotFoundException('Chat room not found for bot closure');
    }

    if (chatRoom.status === 'closed') {
      return chatRoom; // Already closed
    }

    // Update chat room status
    chatRoom.status = 'closed';
    chatRoom.current_handler = 'system';
    chatRoom.closed_at = new Date();
    chatRoom.closed_by_type = 'system';

    return await this.chatRoomRepository.save(chatRoom);
  }

  /**
   * Mark chat as resolved by bot
   */
  async markChatResolved(
    chatRoomId: string,
    reason: string,
  ): Promise<ChatRoom> {
    const chatRoom = await this.chatRoomRepository.findOne({
      where: { id: chatRoomId },
    });

    if (!chatRoom) {
      throw new NotFoundException('Chat room not found for marking resolved');
    }

    // For now, treat resolved as closed with specific reason
    chatRoom.status = 'closed';
    chatRoom.current_handler = 'system';
    chatRoom.closed_at = new Date();
    chatRoom.closed_by_type = 'system';

    return await this.chatRoomRepository.save(chatRoom);
  }

  /**
   * Send system message to chat room
   */
  private async sendSystemMessage(
    chatRoomId: string,
    content: string,
  ): Promise<Message> {
    const chatRoom = await this.chatRoomRepository.findOne({
      where: { id: chatRoomId },
    });

    if (!chatRoom) {
      throw new NotFoundException('Chat room not found for system message');
    }

    // Encrypt message content
    const encryptedContent = this.encryptionService.encrypt(content);

    const systemMessage = this.messageRepository.create({
      chat_room: chatRoom,
      sender_type: 'system',
      content: encryptedContent,
      message_type: 'text',
      status: 'sent',
    });

    const savedMessage = await this.messageRepository.save(systemMessage);

    // Update chat room last message time
    await this.updateChatRoomLastMessage(chatRoomId);

    return savedMessage;
  }
}
