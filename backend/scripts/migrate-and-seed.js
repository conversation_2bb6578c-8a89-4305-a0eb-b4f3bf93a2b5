#!/usr/bin/env node

/**
 * 🚀 Database Migration and Seeding Script
 * 
 * This script will:
 * 1. Run all pending migrations
 * 2. Seed the database with initial data including admin user
 * 
 * Usage:
 * - For development: npm run migrate:dev
 * - For production: npm run migrate:prod
 */

require('dotenv').config({ path: '.env.local' });
const { execSync } = require('child_process');
const bcrypt = require('bcrypt');
const { v4: uuidv4 } = require('uuid');

// Database configuration from environment
const DB_CONFIG = {
  host: process.env.DB_HOST,
  port: process.env.DB_PORT,
  user: process.env.DB_USER,
  password: process.env.DB_PASS,
  database: process.env.DB_NAME,
  ssl: process.env.DB_SSL === 'true'
};

console.log('🔧 Database Migration and Seeding Script');
console.log('==========================================');
console.log(`📍 Target Database: ${DB_CONFIG.database}@${DB_CONFIG.host}:${DB_CONFIG.port}`);
console.log(`🔐 SSL Enabled: ${DB_CONFIG.ssl}`);

async function runMigrations() {
  console.log('\n📦 Running database migrations...');
  try {
    // Build the project first to ensure migrations are compiled
    console.log('🔨 Building project...');
    execSync('npm run build', { stdio: 'inherit', cwd: process.cwd() });
    
    // Run TypeORM migrations
    console.log('🚀 Running TypeORM migrations...');
    execSync('npx typeorm migration:run -d dist/source.js', { 
      stdio: 'inherit', 
      cwd: process.cwd(),
      env: { ...process.env, NODE_ENV: 'development' }
    });
    
    console.log('✅ Migrations completed successfully!');
    return true;
  } catch (error) {
    console.error('❌ Migration failed:', error.message);
    return false;
  }
}

async function runSeeds() {
  console.log('\n🌱 Running database seeds...');
  try {
    // Run the existing seed script
    console.log('📝 Running basic seeds (roles, ticket status)...');
    execSync('npm run seed', { stdio: 'inherit', cwd: process.cwd() });
    
    console.log('✅ Basic seeds completed successfully!');
    return true;
  } catch (error) {
    console.error('❌ Seeding failed:', error.message);
    return false;
  }
}

async function createAdminUser() {
  console.log('\n👤 Creating admin user...');
  
  const pgp = require('pg-promise')();
  
  // Build connection string with SSL support
  const connectionConfig = {
    host: DB_CONFIG.host,
    port: parseInt(DB_CONFIG.port),
    database: DB_CONFIG.database,
    user: DB_CONFIG.user,
    password: DB_CONFIG.password,
    ssl: DB_CONFIG.ssl ? { rejectUnauthorized: false } : false
  };
  
  const db = pgp(connectionConfig);
  
  try {
    // Check if admin user already exists
    const existingAdmin = await db.oneOrNone(
      'SELECT id FROM users WHERE username = $1 OR email = $2',
      ['admin', '<EMAIL>']
    );
    
    if (existingAdmin) {
      console.log('⚠️  Admin user already exists, skipping creation...');
      return true;
    }
    
    // Get Admin role ID
    const adminRole = await db.oneOrNone(
      'SELECT id FROM master_role WHERE name = $1',
      ['Admin']
    );
    
    if (!adminRole) {
      console.error('❌ Admin role not found! Make sure roles are seeded first.');
      return false;
    }
    
    // Generate admin user data
    const adminData = {
      id: uuidv4(),
      nama: 'System Administrator',
      username: 'admin',
      email: '<EMAIL>',
      password: await bcrypt.hash('admin123', 10), // Default password
      roleId: adminRole.id,
      status: 1,
      created_at: new Date(),
      updated_at: new Date()
    };
    
    // Insert admin user
    await db.none(`
      INSERT INTO users (id, nama, username, email, password, "roleId", status, created_at, updated_at)
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
    `, [
      adminData.id,
      adminData.nama,
      adminData.username,
      adminData.email,
      adminData.password,
      adminData.roleId,
      adminData.status,
      adminData.created_at,
      adminData.updated_at
    ]);
    
    console.log('✅ Admin user created successfully!');
    console.log('📋 Admin Credentials:');
    console.log(`   Username: ${adminData.username}`);
    console.log(`   Email: ${adminData.email}`);
    console.log(`   Password: admin123`);
    console.log('⚠️  Please change the default password after first login!');
    
    return true;
  } catch (error) {
    console.error('❌ Failed to create admin user:', error.message);
    return false;
  } finally {
    pgp.end();
  }
}

async function main() {
  console.log('\n🚀 Starting database setup process...\n');
  
  let success = true;
  
  // Step 1: Run migrations
  if (!await runMigrations()) {
    success = false;
  }
  
  // Step 2: Run seeds
  if (success && !await runSeeds()) {
    success = false;
  }
  
  // Step 3: Create admin user
  if (success && !await createAdminUser()) {
    success = false;
  }
  
  console.log('\n==========================================');
  if (success) {
    console.log('🎉 Database setup completed successfully!');
    console.log('\n📝 Next steps:');
    console.log('1. Start your application');
    console.log('2. Login with admin credentials');
    console.log('3. Change the default admin password');
    console.log('4. Create additional users as needed');
  } else {
    console.log('❌ Database setup failed!');
    console.log('Please check the errors above and try again.');
    process.exit(1);
  }
}

// Run the script
if (require.main === module) {
  main().catch(error => {
    console.error('💥 Unexpected error:', error);
    process.exit(1);
  });
}

module.exports = { runMigrations, runSeeds, createAdminUser };
