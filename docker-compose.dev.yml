# Docker Compose configuration for development with Nginx reverse proxy
# For local development: frontend at http://localhost, API at http://localhost/api

version: '3.8'

services:
  # NestJS Backend (Development)
  backend:
    image: ${IMAGE_NAME_BACKEND:-aistechgs/be-wbs-kg}:${TAG:-latest}
    container_name: csrit-backend-dev
    restart: unless-stopped
    env_file:
      - ${ENV_BE:-.env}
    environment:
      NODE_ENV: development
      PORT: 3000
    volumes:
      - backend_dev_uploads:/app/uploads
      - backend_dev_logs:/app/logs
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - csrit-dev-network
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:3000/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
    expose:
      - "3000"

  # Vue.js Frontend (Development build)
  frontend:
    image: ${IMAGE_NAME_FRONTEND:-aistechgs/fe-wbs-kg}:${TAG:-latest}
    container_name: csrit-frontend-dev
    env_file:
      - ${ENV_FE:-.env}
    volumes:
      - frontend_dev_dist:/app/dist
    networks:
      - csrit-dev-network
    command: npm run build

  # Nginx Reverse Proxy (Development)
  nginx:
    image: nginx:alpine
    container_name: csrit-nginx-dev
    restart: unless-stopped
    ports:
      - "80:80"
    volumes:
      # Development nginx configuration
      - ./nginx/nginx.dev.conf:/etc/nginx/nginx.conf:ro
      # Frontend static files
      - frontend_dev_dist:/usr/share/nginx/html:ro
      # Logs
      - nginx_dev_logs:/var/log/nginx
    depends_on:
      - backend
      - frontend
    networks:
      - csrit-dev-network
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3



volumes:
  postgres_dev_data:
    driver: local
  backend_dev_uploads:
    driver: local
  backend_dev_logs:
    driver: local
  frontend_dev_dist:
    driver: local
  nginx_dev_logs:
    driver: local

networks:
  csrit-dev-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
