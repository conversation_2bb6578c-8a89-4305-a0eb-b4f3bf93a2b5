# Docker Compose configuration for cloud deployment with exposed ports
# Backend exposed on port 3000, Frontend exposed on port 80
# Use with Nginx Proxy Manager for reverse proxy

version: '3.8'

services:
  # NestJS Backend
  backend:
    image: ${IMAGE_NAME_BACKEND:-aistechgs/be-wbs-kg}:${TAG:-latest}
    container_name: csrit-backend-dev
    restart: unless-stopped
    env_file:
      - ${ENV_BE:-.env}
    environment:
      NODE_ENV: development
      PORT: 3000
    volumes:
      - backend_dev_uploads:/app/uploads
      - backend_dev_logs:/app/logs
    ports:
      - "3010:3000"
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:3000/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Vue.js Frontend
  frontend:
    image: ${IMAGE_NAME_FRONTEND:-aistechgs/fe-wbs-kg}:${TAG:-latest}
    container_name: csrit-frontend-dev
    restart: unless-stopped
    env_file:
      - ${ENV_FE:-.env}
    volumes:
      - frontend_dev_dist:/app/dist
    ports:
      - "8000:80"
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:80 || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  backend_dev_uploads:
    driver: local
  backend_dev_logs:
    driver: local
  frontend_dev_dist:
    driver: local
