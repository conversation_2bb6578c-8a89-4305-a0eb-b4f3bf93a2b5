#!/bin/bash

# SSL Certificate Generation Script
# Generates self-signed certificates for development and provides instructions for production

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
DOMAIN="foobar.com"
SSL_DIR="./ssl"
CERTS_DIR="${SSL_DIR}/certs"
PRIVATE_DIR="${SSL_DIR}/private"

echo -e "${BLUE}SSL Certificate Generation Script${NC}"
echo -e "${BLUE}=================================${NC}"

# Create SSL directories
echo -e "${YELLOW}Creating SSL directories...${NC}"
mkdir -p "${CERTS_DIR}"
mkdir -p "${PRIVATE_DIR}"

# Set proper permissions
chmod 755 "${SSL_DIR}"
chmod 755 "${CERTS_DIR}"
chmod 700 "${PRIVATE_DIR}"

# Function to generate self-signed certificate for development
generate_dev_cert() {
    echo -e "${YELLOW}Generating self-signed certificate for development...${NC}"
    
    # Generate private key
    openssl genrsa -out "${PRIVATE_DIR}/${DOMAIN}.key" 2048
    
    # Generate certificate signing request
    openssl req -new -key "${PRIVATE_DIR}/${DOMAIN}.key" -out "${SSL_DIR}/${DOMAIN}.csr" \
        -subj "/C=ID/ST=South Sulawesi/L=Makassar/O=Bank Sulsel/OU=CSRIT/CN=${DOMAIN}/emailAddress=admin@${DOMAIN}"
    
    # Generate self-signed certificate
    openssl x509 -req -days 365 -in "${SSL_DIR}/${DOMAIN}.csr" \
        -signkey "${PRIVATE_DIR}/${DOMAIN}.key" \
        -out "${CERTS_DIR}/${DOMAIN}.crt" \
        -extensions v3_req \
        -extfile <(cat <<EOF
[v3_req]
keyUsage = keyEncipherment, dataEncipherment
extendedKeyUsage = serverAuth
subjectAltName = @alt_names

[alt_names]
DNS.1 = ${DOMAIN}
DNS.2 = www.${DOMAIN}
DNS.3 = localhost
IP.1 = 127.0.0.1
EOF
)
    
    # Set proper permissions
    chmod 600 "${PRIVATE_DIR}/${DOMAIN}.key"
    chmod 644 "${CERTS_DIR}/${DOMAIN}.crt"
    
    # Clean up CSR
    rm "${SSL_DIR}/${DOMAIN}.csr"
    
    echo -e "${GREEN}✓ Self-signed certificate generated successfully!${NC}"
    echo -e "${YELLOW}Certificate: ${CERTS_DIR}/${DOMAIN}.crt${NC}"
    echo -e "${YELLOW}Private Key: ${PRIVATE_DIR}/${DOMAIN}.key${NC}"
    echo ""
    echo -e "${RED}⚠️  WARNING: This is a self-signed certificate for development only!${NC}"
    echo -e "${RED}   Browsers will show security warnings.${NC}"
    echo ""
}

# Function to show production certificate instructions
show_production_instructions() {
    echo -e "${BLUE}Production SSL Certificate Instructions${NC}"
    echo -e "${BLUE}=======================================${NC}"
    echo ""
    echo -e "${YELLOW}For production deployment, you need a valid SSL certificate from a Certificate Authority.${NC}"
    echo ""
    echo -e "${GREEN}Option 1: Let's Encrypt (Free)${NC}"
    echo "1. Install Certbot:"
    echo "   sudo apt-get update"
    echo "   sudo apt-get install certbot python3-certbot-nginx"
    echo ""
    echo "2. Generate certificate:"
    echo "   sudo certbot --nginx -d ${DOMAIN} -d www.${DOMAIN}"
    echo ""
    echo "3. Copy certificates to project:"
    echo "   sudo cp /etc/letsencrypt/live/${DOMAIN}/fullchain.pem ${CERTS_DIR}/${DOMAIN}.crt"
    echo "   sudo cp /etc/letsencrypt/live/${DOMAIN}/privkey.pem ${PRIVATE_DIR}/${DOMAIN}.key"
    echo "   sudo chown \$(whoami):\$(whoami) ${CERTS_DIR}/${DOMAIN}.crt ${PRIVATE_DIR}/${DOMAIN}.key"
    echo ""
    echo -e "${GREEN}Option 2: Commercial Certificate${NC}"
    echo "1. Generate CSR:"
    echo "   openssl req -new -newkey rsa:2048 -nodes -keyout ${PRIVATE_DIR}/${DOMAIN}.key -out ${DOMAIN}.csr"
    echo ""
    echo "2. Submit CSR to your Certificate Authority"
    echo "3. Download the certificate and place it at: ${CERTS_DIR}/${DOMAIN}.crt"
    echo ""
    echo -e "${GREEN}Option 3: Cloudflare (if using Cloudflare)${NC}"
    echo "1. Enable SSL/TLS in Cloudflare dashboard"
    echo "2. Set SSL/TLS encryption mode to 'Full (strict)'"
    echo "3. Generate Origin Certificate in Cloudflare"
    echo "4. Place certificate and key in the SSL directories"
    echo ""
    echo -e "${YELLOW}After obtaining production certificates:${NC}"
    echo "- Ensure certificate file is at: ${CERTS_DIR}/${DOMAIN}.crt"
    echo "- Ensure private key is at: ${PRIVATE_DIR}/${DOMAIN}.key"
    echo "- Set proper permissions: chmod 600 ${PRIVATE_DIR}/${DOMAIN}.key"
    echo "- Set proper permissions: chmod 644 ${CERTS_DIR}/${DOMAIN}.crt"
    echo ""
}

# Main script logic
if [[ "$1" == "dev" || "$1" == "development" ]]; then
    generate_dev_cert
elif [[ "$1" == "prod" || "$1" == "production" ]]; then
    show_production_instructions
else
    echo -e "${YELLOW}Usage: $0 [dev|prod]${NC}"
    echo ""
    echo -e "${YELLOW}Commands:${NC}"
    echo "  dev   - Generate self-signed certificate for development"
    echo "  prod  - Show instructions for production certificate setup"
    echo ""
    echo -e "${YELLOW}Examples:${NC}"
    echo "  $0 dev   # Generate development certificate"
    echo "  $0 prod  # Show production instructions"
    exit 1
fi
